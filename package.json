{"name": "personal-budget-allocation-app", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "deploy": "vercel --prod", "mobile:build": "npm run build && npx cap sync", "mobile:android": "npm run mobile:build && npx cap open android", "mobile:ios": "npm run mobile:build && npx cap open ios", "mobile:run:android": "npm run mobile:build && npx cap run android", "mobile:run:ios": "npm run mobile:build && npx cap run ios"}, "dependencies": {"@capacitor/android": "^7.2.0", "@capacitor/app": "^7.0.1", "@capacitor/cli": "^7.2.0", "@capacitor/core": "^7.2.0", "@capacitor/ios": "^7.2.0", "@capacitor/preferences": "^7.0.1", "@capacitor/status-bar": "^7.0.1", "@capgo/capacitor-native-biometric": "^7.1.7", "firebase": "^11.8.1", "framer-motion": "^11.2.10", "median-js-bridge": "^2.8.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.23.1", "react-toastify": "^10.0.5", "recharts": "^2.15.3", "swiper": "^11.1.4"}, "devDependencies": {"@types/node": "^22.14.0", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.0", "@types/recharts": "^2.0.1", "@vitejs/plugin-react": "^4.2.1", "typescript": "~5.7.2", "vite": "^6.2.0"}}