import{W as t}from"./index-E66gLKHg.js";class n extends t{constructor(){super()}isAvailable(){throw new Error("Method not implemented.")}verifyIdentity(e){throw console.log("verifyIdentity",e),new Error("Method not implemented.")}getCredentials(e){throw console.log("getCredentials",e),new Error("Method not implemented.")}setCredentials(e){throw console.log("setCredentials",e),new Error("Method not implemented.")}deleteCredentials(e){throw console.log("deleteCredentials",e),new Error("Method not implemented.")}}export{n as NativeBiometricWeb};
