
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no" />
    <title>Budget Tracker Pro</title>
    <meta name="description" content="Personal budget allocation and tracking app" />
    <meta name="theme-color" content="#3b82f6" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="Budget Tracker" />
    <link rel="manifest" href="/manifest.json" />
    <link rel="icon" type="image/png" sizes="192x192" href="/icon-192.png" />
    <link rel="icon" type="image/png" sizes="512x512" href="/icon-512.png" />
    <link rel="apple-touch-icon" href="/icon-192.png" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            animation: {
              'spin-slow': 'spin 8s linear infinite',
              'float': 'float 3s ease-in-out infinite',
              'gradient': 'gradient-shift 4s ease infinite',
              'pulse-glow': 'pulse-glow 2s ease-in-out infinite'
            },
            keyframes: {
              float: {
                '0%, 100%': { transform: 'translateY(0px) scale(1)', opacity: '0.7' },
                '25%': { transform: 'translateY(-3px) scale(1.1)', opacity: '1' },
                '50%': { transform: 'translateY(-1px) scale(0.9)', opacity: '0.8' },
                '75%': { transform: 'translateY(-2px) scale(1.05)', opacity: '0.9' }
              },
              'gradient-shift': {
                '0%, 100%': { 'background-position': '0% 50%' },
                '50%': { 'background-position': '100% 50%' }
              },
              'pulse-glow': {
                '0%, 100%': { 'box-shadow': '0 0 5px rgba(139, 92, 246, 0.3)' },
                '50%': { 'box-shadow': '0 0 20px rgba(139, 92, 246, 0.6), 0 0 30px rgba(139, 92, 246, 0.4)' }
              }
            }
          }
        }
      }
    </script>
    <style>
      body {
        font-family: 'Inter', sans-serif; /* A clean, modern font often used with Tailwind */
      }
      /* Custom scrollbar for better aesthetics in dark mode */
      ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }
      ::-webkit-scrollbar-track {
        background: #1e293b; /* slate-800 */
      }
      ::-webkit-scrollbar-thumb {
        background: #475569; /* slate-600 */
        border-radius: 4px;
      }
      ::-webkit-scrollbar-thumb:hover {
        background: #64748b; /* slate-500 */
      }
      
      /* Custom animations for enhanced logo */
      @keyframes animate-spin-slow {
        from {
          transform: rotate(0deg);
        }
        to {
          transform: rotate(360deg);
        }
      }
      
      @keyframes float {
        0%, 100% {
          transform: translateY(0px) scale(1);
          opacity: 0.7;
        }
        25% {
          transform: translateY(-3px) scale(1.1);
          opacity: 1;
        }
        50% {
          transform: translateY(-1px) scale(0.9);
          opacity: 0.8;
        }
        75% {
          transform: translateY(-2px) scale(1.05);
          opacity: 0.9;
        }
      }
      
      @keyframes gradient-shift {
        0%, 100% {
          background-position: 0% 50%;
        }
        50% {
          background-position: 100% 50%;
        }
      }
      
      @keyframes pulse-glow {
        0%, 100% {
          box-shadow: 0 0 5px rgba(139, 92, 246, 0.3);
        }
        50% {
          box-shadow: 0 0 20px rgba(139, 92, 246, 0.6), 0 0 30px rgba(139, 92, 246, 0.4);
        }
      }
      
      .animate-spin-slow {
        animation: animate-spin-slow 8s linear infinite;
      }
      
      .animate-float {
        animation: float 3s ease-in-out infinite;
      }
      
      .animate-gradient {
        animation: gradient-shift 4s ease infinite;
        background-size: 400% 400%;
      }
      
      .animate-pulse-glow {
        animation: pulse-glow 2s ease-in-out infinite;
      }
    </style>
    <script type="module" crossorigin src="/assets/index-E66gLKHg.js"></script>
  </head>
  <body class="bg-slate-900 text-slate-100 antialiased">
    <div id="root"></div>
  </body>
</html>
    